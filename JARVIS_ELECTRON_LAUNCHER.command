#!/bin/bash

# 🚀 LANCEUR ELECTRON JARVIS VALIDÉ - JEAN-LUC PASSAVE
# Double-clic pour démarrer l'application validée sur port 7864

echo "🤖 JARVIS ELECTRON LAUNCHER VALIDÉ"
echo "=================================="

# Aller dans le bon répertoire
cd "$(dirname "$0")"

echo "📁 Répertoire: $(pwd)"

# Nettoyer les anciens processus
echo "🧹 Nettoyage des anciens processus..."
pkill -f "jarvis_interface_communication_principale.py" 2>/dev/null || true
pkill -f "jarvis_architecture_multi_fenetres.py" 2>/dev/null || true
pkill -f "electron.*jarvis" 2>/dev/null || true
pkill -f "port.*7864" 2>/dev/null || true
sleep 3

# Vérifier les fichiers
if [ -f "jarvis_electron_nouveau.js" ]; then
    echo "✅ Application Electron validée trouvée"
else
    echo "❌ Application Electron manquante"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if [ -f "jarvis_interface_communication_principale.py" ]; then
    echo "✅ Interface JARVIS Dashboard (port 7864) trouvée"
else
    echo "❌ Interface Dashboard manquante"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

# Vérifier Electron
if [ -f "node_modules/.bin/electron" ]; then
    echo "✅ Electron installé"
else
    echo "🔧 Installation d'Electron..."
    npm install
fi

echo ""
echo "🚀 DÉMARRAGE JARVIS DASHBOARD (PORT 7864)..."
echo "🧠 Interface validée Jean-Luc Passave"

# Démarrer l'interface Dashboard sur port 7864 avec le bon environnement
source venv_gradio/bin/activate && python3 jarvis_interface_communication_principale.py &
JARVIS_PID=$!
echo "✅ JARVIS Dashboard démarré (PID: $JARVIS_PID) sur port 7864"

# Attendre que l'interface soit prête
echo "⏳ Attente du démarrage complet de l'interface..."
sleep 8

# Vérifier que le port 7864 est actif
echo "🔍 Vérification du port 7864..."
if curl -s http://localhost:7864 > /dev/null; then
    echo "✅ Interface JARVIS accessible sur port 7864"
else
    echo "⚠️ Interface en cours de démarrage..."
    sleep 5
fi

echo ""
echo "🖥️ DÉMARRAGE APPLICATION ELECTRON VALIDÉE..."
echo "🎯 Interface Dashboard violette validée"
echo "🧠 QI 648 + 100M neurones actifs"
echo ""

# Démarrer l'application Electron validée
npm start

echo ""
echo "🔄 Application fermée"
echo "🛑 Arrêt de JARVIS..."
kill $JARVIS_PID 2>/dev/null || true
read -p "Appuyez sur Entrée pour fermer cette fenêtre..."
